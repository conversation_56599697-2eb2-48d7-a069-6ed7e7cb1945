{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host --open", "dev:stagewise": "cross-env NUXT_PUBLIC_STAGEWISE_TOOLBAR_ENABLED=true nuxt dev --host --open", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "env:encrypt": "node dev-tools/env/encrypt-decrypt.js encrypt", "env:decrypt": "node dev-tools/env/encrypt-decrypt.js decrypt"}, "dependencies": {"@auth/core": "^0.37.4", "@dotenvx/dotenvx": "^1.38.3", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@nuxt/image": "1.8.1", "@nuxtjs/seo": "^3.0.2", "@nuxtjs/sitemap": "^7.2.10", "@nuxtjs/tailwindcss": "6.12.2", "@opendocsg/pdf2md": "^0.2.1", "@pinia/nuxt": "^0.9.0", "@sidebase/nuxt-auth": "^0.10.0", "@stagewise/toolbar": "^0.2.1", "@supabase/supabase-js": "^2.49.4", "@types/crypto-js": "^4.2.2", "@vueuse/core": "^12.0.0", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.7", "crypto-js": "^4.2.0", "events": "^3.3.0", "h3": "^1.15.1", "lodash-es": "^4.17.21", "mailersend": "^2.4.0", "markdown-it": "^14.1.0", "nuxt": "^3.14.1592", "openai": "^4.80.1", "pinia": "^2.3.0", "redis": "^4.7.0", "resend": "^4.6.0", "unique-names-generator": "^4.7.1", "uuid": "^11.1.0", "vue": "latest", "vue-chartjs": "^5.3.2", "vue-router": "latest", "vue-toast-notification": "^3.1.3", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/canvas-confetti": "^1.9.0", "@types/lodash-es": "^4.17.12", "@types/ws": "^8.18.1", "bcryptjs": "^2.4.3", "cross-env": "^7.0.3", "mongoose": "^8.8.4", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.9", "sass-embedded": "^1.82.0", "vite-svg-loader": "^5.1.0", "winston": "^3.17.0", "zod": "^3.23.8"}, "packageManager": "pnpm@9.15.0"}